import logging
from typing import Optional, List

from fastapi import APIRouter, Depends, HTTPException, Request, status
from pydantic import BaseModel

from open_webui.models.note_folders import (
    NoteFolders,
    NoteFolderModel,
    NoteFolderForm,
    NoteFolderUpdateForm,
    NoteFolderTreeNode,
    NoteFolderMoveForm,
)
from open_webui.models.users import Users, UserResponse
from open_webui.models.notes import Notes, NoteUserResponse
from open_webui.constants import ERROR_MESSAGES
from open_webui.env import SRC_LOG_LEVELS
from open_webui.utils.auth import get_verified_user
from open_webui.utils.access_control import has_permission

log = logging.getLogger(__name__)
log.setLevel(SRC_LOG_LEVELS["MODELS"])

router = APIRouter()

############################
# GetFolders
############################


@router.get("/", response_model=List[NoteFolderModel])
async def get_folders(request: Request, user=Depends(get_verified_user)):
    """獲取用戶的所有資料夾"""
    if user.role != "admin" and not has_permission(
        user.id, "features.notes", request.app.state.config.USER_PERMISSIONS
    ):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=ERROR_MESSAGES.UNAUTHORIZED,
        )

    try:
        folders = NoteFolders.get_folders_by_user_id(user.id)
        return folders
    except Exception as e:
        log.exception(e)
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail=ERROR_MESSAGES.DEFAULT()
        )


@router.get("/tree", response_model=List[NoteFolderTreeNode])
async def get_folder_tree(request: Request, user=Depends(get_verified_user)):
    """獲取用戶的資料夾樹狀結構"""
    if user.role != "admin" and not has_permission(
        user.id, "features.notes", request.app.state.config.USER_PERMISSIONS
    ):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=ERROR_MESSAGES.UNAUTHORIZED,
        )

    try:
        tree = NoteFolders.get_folder_tree(user.id)
        return tree
    except Exception as e:
        log.exception(e)
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail=ERROR_MESSAGES.DEFAULT()
        )


############################
# CreateFolder
############################


@router.post("/", response_model=Optional[NoteFolderModel])
async def create_folder(
    request: Request, form_data: NoteFolderForm, user=Depends(get_verified_user)
):
    """創建新資料夾"""
    if user.role != "admin" and not has_permission(
        user.id, "features.notes", request.app.state.config.USER_PERMISSIONS
    ):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=ERROR_MESSAGES.UNAUTHORIZED,
        )

    try:
        folder = NoteFolders.insert_new_folder(form_data, user.id)
        return folder
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail=str(e)
        )
    except Exception as e:
        log.exception(e)
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail=ERROR_MESSAGES.DEFAULT()
        )


############################
# GetFolderById
############################


@router.get("/{folder_id}", response_model=Optional[NoteFolderModel])
async def get_folder_by_id(
    request: Request, folder_id: str, user=Depends(get_verified_user)
):
    """根據ID獲取資料夾"""
    if user.role != "admin" and not has_permission(
        user.id, "features.notes", request.app.state.config.USER_PERMISSIONS
    ):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=ERROR_MESSAGES.UNAUTHORIZED,
        )

    try:
        folder = NoteFolders.get_folder_by_id(folder_id, user.id)
        if not folder:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Folder not found"
            )
        return folder
    except HTTPException:
        raise
    except Exception as e:
        log.exception(e)
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail=ERROR_MESSAGES.DEFAULT()
        )


############################
# UpdateFolder
############################


@router.put("/{folder_id}", response_model=Optional[NoteFolderModel])
async def update_folder_by_id(
    request: Request,
    folder_id: str,
    form_data: NoteFolderUpdateForm,
    user=Depends(get_verified_user),
):
    """更新資料夾信息"""
    if user.role != "admin" and not has_permission(
        user.id, "features.notes", request.app.state.config.USER_PERMISSIONS
    ):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=ERROR_MESSAGES.UNAUTHORIZED,
        )

    try:
        folder = NoteFolders.update_folder_by_id(folder_id, form_data, user.id)
        if not folder:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Folder not found"
            )
        return folder
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        log.exception(e)
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail=ERROR_MESSAGES.DEFAULT()
        )


############################
# DeleteFolder
############################


@router.delete("/{folder_id}")
async def delete_folder_by_id(
    request: Request, folder_id: str, user=Depends(get_verified_user)
):
    """刪除資料夾"""
    if user.role != "admin" and not has_permission(
        user.id, "features.notes", request.app.state.config.USER_PERMISSIONS
    ):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=ERROR_MESSAGES.UNAUTHORIZED,
        )

    try:
        result = NoteFolders.delete_folder_by_id(folder_id, user.id)
        if not result:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Folder not found"
            )
        return {"message": "Folder deleted successfully"}
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        log.exception(e)
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail=ERROR_MESSAGES.DEFAULT()
        )


############################
# MoveFolder
############################


@router.post("/{folder_id}/move", response_model=Optional[NoteFolderModel])
async def move_folder(
    request: Request,
    folder_id: str,
    form_data: NoteFolderMoveForm,
    user=Depends(get_verified_user),
):
    """移動資料夾到新位置"""
    if user.role != "admin" and not has_permission(
        user.id, "features.notes", request.app.state.config.USER_PERMISSIONS
    ):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=ERROR_MESSAGES.UNAUTHORIZED,
        )

    try:
        folder = NoteFolders.move_folder(folder_id, form_data, user.id)
        if not folder:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Folder not found"
            )
        return folder
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        log.exception(e)
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail=ERROR_MESSAGES.DEFAULT()
        )


############################
# GetFolderPath
############################


@router.get("/{folder_id}/path", response_model=List[NoteFolderModel])
async def get_folder_path(
    request: Request, folder_id: str, user=Depends(get_verified_user)
):
    """獲取資料夾的完整路徑"""
    if user.role != "admin" and not has_permission(
        user.id, "features.notes", request.app.state.config.USER_PERMISSIONS
    ):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=ERROR_MESSAGES.UNAUTHORIZED,
        )

    try:
        path = NoteFolders.get_folder_path(folder_id, user.id)
        return path
    except Exception as e:
        log.exception(e)
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail=ERROR_MESSAGES.DEFAULT()
        )


############################
# GetNotesInRoot
############################


@router.get("/root/notes", response_model=List[NoteUserResponse])
async def get_notes_in_root(
    request: Request,
    user=Depends(get_verified_user),
):
    """獲取根目錄（未分類）中的筆記"""
    if user.role != "admin" and not has_permission(
        user.id, "features.notes", request.app.state.config.USER_PERMISSIONS
    ):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=ERROR_MESSAGES.UNAUTHORIZED,
        )

    try:
        # 獲取沒有資料夾的筆記
        notes = Notes.get_notes_by_folder_id(None, user.id)

        return [
            NoteUserResponse(
                **{
                    **note.model_dump(),
                    "user": UserResponse(**Users.get_user_by_id(note.user_id).model_dump()),
                }
            )
            for note in notes
        ]
    except Exception as e:
        log.exception(e)
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail=ERROR_MESSAGES.DEFAULT()
        )
