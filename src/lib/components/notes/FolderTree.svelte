<script lang="ts">
	import { createEventDispatcher, getContext } from 'svelte';
	import { toast } from 'svelte-sonner';
	import { 
		getNoteFolderTree, 
		createNoteFolder, 
		updateNoteFolder, 
		deleteNoteFolder,
		type NoteFolderTreeNode,
		type NoteFolderForm 
	} from '$lib/apis/note-folders';
	
	// Icons
	import ChevronRight from '$lib/components/icons/ChevronRight.svelte';
	import ChevronDown from '$lib/components/icons/ChevronDown.svelte';
	import Folder from '$lib/components/icons/Folder.svelte';
	import FolderOpen from '$lib/components/icons/FolderOpen.svelte';
	import Plus from '$lib/components/icons/Plus.svelte';
	import DotsHorizontal from '$lib/components/icons/EllipsisHorizontal.svelte';
	import Pencil from '$lib/components/icons/Pencil.svelte';
	import Trash from '$lib/components/icons/GarbageBin.svelte';

	const i18n = getContext('i18n');
	const dispatch = createEventDispatcher();

	export let selectedFolderId: string | null = null;
	export let expandedFolders: Set<string> = new Set();
	
	// 支援遞歸渲染的 props
	export let folder: NoteFolderTreeNode | null = null;
	export let level: number = 0;

	let folderTree: NoteFolderTreeNode[] = [];
	let loading = false;
	let showCreateDialog = false;
	let showEditDialog = false;
	let showDeleteDialog = false;
	let currentFolder: NoteFolderTreeNode | null = null;
	let newFolderName = '';
	let newFolderDescription = '';
	let newFolderColor = '#3b82f6';
	let newFolderParentId: string | null = null;

	// 初始化資料夾樹
	const init = async () => {
		loading = true;
		try {
			folderTree = await getNoteFolderTree(localStorage.token);
		} catch (error) {
			toast.error(`${error}`);
		} finally {
			loading = false;
		}
	};

	// 切換資料夾展開狀態
	const toggleFolder = (folderId: string) => {
		if (expandedFolders.has(folderId)) {
			expandedFolders.delete(folderId);
		} else {
			expandedFolders.add(folderId);
		}
		expandedFolders = expandedFolders;
	};

	// 選擇資料夾
	const selectFolder = (folderId: string | null) => {
		selectedFolderId = folderId;
		dispatch('folderSelected', { folderId });
	};

	// 顯示創建資料夾對話框
	const showCreateFolderDialog = (parentId: string | null = null) => {
		newFolderName = '';
		newFolderDescription = '';
		newFolderColor = '#3b82f6';
		newFolderParentId = parentId;
		showCreateDialog = true;
	};

	// 創建資料夾
	const createFolder = async () => {
		if (!newFolderName.trim()) {
			toast.error($i18n.t('Folder name is required'));
			return;
		}

		try {
			const folderData: NoteFolderForm = {
				name: newFolderName.trim(),
				description: newFolderDescription.trim() || undefined,
				color: newFolderColor,
				parent_id: newFolderParentId,
				sort_order: 0
			};

			await createNoteFolder(localStorage.token, folderData);
			toast.success($i18n.t('Folder created successfully'));
			showCreateDialog = false;
			await init();
		} catch (error) {
			toast.error(`${error}`);
		}
	};

	// 顯示編輯資料夾對話框
	const showEditFolderDialog = (folder: NoteFolderTreeNode) => {
		currentFolder = folder;
		newFolderName = folder.name;
		newFolderDescription = folder.description || '';
		newFolderColor = folder.color || '#3b82f6';
		showEditDialog = true;
	};

	// 編輯資料夾
	const editFolder = async () => {
		if (!currentFolder || !newFolderName.trim()) {
			toast.error($i18n.t('Folder name is required'));
			return;
		}

		try {
			await updateNoteFolder(localStorage.token, currentFolder.id, {
				name: newFolderName.trim(),
				description: newFolderDescription.trim() || undefined,
				color: newFolderColor
			});
			toast.success($i18n.t('Folder updated successfully'));
			showEditDialog = false;
			await init();
		} catch (error) {
			toast.error(`${error}`);
		}
	};

	// 顯示刪除資料夾對話框
	const showDeleteFolderDialog = (folder: NoteFolderTreeNode) => {
		currentFolder = folder;
		showDeleteDialog = true;
	};

	// 刪除資料夾
	const deleteFolder = async () => {
		if (!currentFolder) return;

		try {
			await deleteNoteFolder(localStorage.token, currentFolder.id);
			toast.success($i18n.t('Folder deleted successfully'));
			showDeleteDialog = false;
			await init();
		} catch (error) {
			toast.error(`${error}`);
		}
	};

	// 渲染資料夾節點
	const renderFolderNode = (node: NoteFolderTreeNode, level: number = 0) => {
		const isExpanded = expandedFolders.has(node.id);
		const isSelected = selectedFolderId === node.id;
		const hasChildren = node.children && node.children.length > 0;

		return {
			node,
			level,
			isExpanded,
			isSelected,
			hasChildren
		};
	};

	// 處理資料夾切換事件
	const handleToggleFolder = (event) => {
		toggleFolder(event.detail.folderId);
	};

	// 處理資料夾選擇事件
	const handleSelectFolder = (event) => {
		selectFolder(event.detail.folderId);
	};

	// 處理創建子資料夾事件
	const handleCreateSubfolder = (event) => {
		showCreateFolderDialog(event.detail.parentId);
	};

	// 處理編輯資料夾事件
	const handleEditFolder = (event) => {
		showEditFolderDialog(event.detail.folder);
	};

	// 處理刪除資料夾事件
	const handleDeleteFolder = (event) => {
		showDeleteFolderDialog(event.detail.folder);
	};

	// 初始化（只在根組件執行）
	if (!folder) {
		init();
	}
</script>

{#if folder}
	<!-- 遞歸渲染單個資料夾 -->
	{@const isExpanded = expandedFolders.has(folder.id)}
	{@const isSelected = selectedFolderId === folder.id}
	{@const hasChildren = folder.children && folder.children.length > 0}
	
	<div class="folder-node" style="padding-left: {level * 16 + 8}px">
		<div class="flex items-center group hover:bg-gray-100 dark:hover:bg-gray-700 rounded px-2 py-1">
			<!-- 展開/收縮按鈕 -->
			{#if hasChildren}
				<button
					type="button"
					class="p-0.5 mr-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
					on:click={() => dispatch('toggle', { folderId: folder.id })}
				>
					{#if isExpanded}
						<ChevronDown className="w-3 h-3" />
					{:else}
						<ChevronRight className="w-3 h-3" />
					{/if}
				</button>
			{:else}
				<div class="w-4 h-4 mr-1"></div>
			{/if}

			<!-- 資料夾圖標 -->
			<div class="mr-2" style="color: {folder.color || '#3b82f6'}">
				{#if isExpanded && hasChildren}
					<FolderOpen className="w-4 h-4" />
				{:else}
					<Folder className="w-4 h-4" />
				{/if}
			</div>

			<!-- 資料夾名稱 -->
			<button
				type="button"
				class="flex-1 text-left text-sm {isSelected ? 'text-blue-700 dark:text-blue-300 font-medium' : 'text-gray-700 dark:text-gray-300'}"
				on:click={() => dispatch('select', { folderId: folder.id })}
			>
				{folder.name}
			</button>

			<!-- 筆記數量 -->
			{#if folder.total_note_count > 0}
				<span class="text-xs text-gray-400 mr-2">
					{folder.total_note_count}
				</span>
			{/if}

			<!-- 操作按鈕 -->
			<div class="opacity-0 group-hover:opacity-100 flex items-center space-x-1">
				<button
					type="button"
					class="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded"
					on:click={() => dispatch('createSubfolder', { parentId: folder.id })}
					title={$i18n.t('Create Subfolder')}
				>
					<Plus className="w-3 h-3" />
				</button>
				<button
					type="button"
					class="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded"
					on:click={() => dispatch('editFolder', { folder })}
					title={$i18n.t('Edit Folder')}
				>
					<Pencil className="w-3 h-3" />
				</button>
				<button
					type="button"
					class="p-1 text-gray-400 hover:text-red-600 dark:hover:text-red-400 rounded"
					on:click={() => dispatch('deleteFolder', { folder })}
					title={$i18n.t('Delete Folder')}
				>
					<Trash className="w-3 h-3" />
				</button>
			</div>
		</div>

		<!-- 遞歸渲染子資料夾 -->
		{#if isExpanded && hasChildren}
			{#each folder.children as childFolder (childFolder.id)}
				<svelte:self
					folder={childFolder}
					level={level + 1}
					{selectedFolderId}
					{expandedFolders}
					on:toggle
					on:select
					on:createSubfolder
					on:editFolder
					on:deleteFolder
				/>
			{/each}
		{/if}
	</div>
{:else}
	<!-- 根組件渲染 -->
	<div class="folder-tree">
		<!-- 頭部 -->
		<div class="flex items-center justify-between p-3 border-b border-gray-200 dark:border-gray-700">
			<h3 class="text-sm font-medium text-gray-900 dark:text-white">
				{$i18n.t('Folders')}
			</h3>
			<button
				type="button"
				class="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded"
				on:click={() => showCreateFolderDialog()}
				title={$i18n.t('Create Folder')}
			>
				<Plus className="w-4 h-4" />
			</button>
		</div>

		<!-- 根目錄 -->
		<div class="p-2">
			<button
				type="button"
				class="w-full flex items-center px-2 py-1.5 text-sm rounded hover:bg-gray-100 dark:hover:bg-gray-700 {selectedFolderId === null ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300' : 'text-gray-700 dark:text-gray-300'}"
				on:click={() => selectFolder(null)}
			>
				<Folder className="w-4 h-4 mr-2" />
				<span class="flex-1 text-left">{$i18n.t('Root')}</span>
			</button>
		</div>

		<!-- 資料夾樹 -->
		<div class="flex-1 overflow-y-auto">
			{#if loading}
				<div class="flex items-center justify-center p-4">
					<div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
				</div>
			{:else if folderTree.length === 0}
				<div class="p-4 text-center text-gray-500 dark:text-gray-400 text-sm">
					{$i18n.t('No folders yet')}
				</div>
			{:else}
				{#each folderTree as rootFolder (rootFolder.id)}
					<svelte:self
						folder={rootFolder}
						level={0}
						{selectedFolderId}
						{expandedFolders}
						on:toggle={handleToggleFolder}
						on:select={handleSelectFolder}
						on:createSubfolder={handleCreateSubfolder}
						on:editFolder={handleEditFolder}
						on:deleteFolder={handleDeleteFolder}
					/>
				{/each}
			{/if}
		</div>
	</div>
{/if}

<!-- 創建資料夾對話框 -->
{#if showCreateDialog}
	<div class="fixed inset-0 bg-gray-900/50 flex items-center justify-center z-50">
		<div class="bg-white dark:bg-gray-800 rounded-lg p-6 w-96 max-w-full mx-4">
			<h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
				{$i18n.t('Create Folder')}
			</h3>
			
			<div class="space-y-4">
				<div>
					<label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
						{$i18n.t('Name')}
					</label>
					<input
						type="text"
						bind:value={newFolderName}
						class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
						placeholder={$i18n.t('Enter folder name')}
					/>
				</div>
				
				<div>
					<label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
						{$i18n.t('Description')} ({$i18n.t('Optional')})
					</label>
					<textarea
						bind:value={newFolderDescription}
						class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
						rows="2"
						placeholder={$i18n.t('Enter folder description')}
					></textarea>
				</div>
				
				<div>
					<label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
						{$i18n.t('Color')}
					</label>
					<input
						type="color"
						bind:value={newFolderColor}
						class="w-full h-10 border border-gray-300 dark:border-gray-600 rounded-md"
					/>
				</div>
			</div>
			
			<div class="flex justify-end space-x-3 mt-6">
				<button
					type="button"
					class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600"
					on:click={() => showCreateDialog = false}
				>
					{$i18n.t('Cancel')}
				</button>
				<button
					type="button"
					class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700"
					on:click={createFolder}
				>
					{$i18n.t('Create')}
				</button>
			</div>
		</div>
	</div>
{/if}

<!-- 編輯資料夾對話框 -->
{#if showEditDialog}
	<div class="fixed inset-0 bg-gray-900/50 flex items-center justify-center z-50">
		<div class="bg-white dark:bg-gray-800 rounded-lg p-6 w-96 max-w-full mx-4">
			<h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
				{$i18n.t('Edit Folder')}
			</h3>
			
			<div class="space-y-4">
				<div>
					<label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
						{$i18n.t('Name')}
					</label>
					<input
						type="text"
						bind:value={newFolderName}
						class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
						placeholder={$i18n.t('Enter folder name')}
					/>
				</div>
				
				<div>
					<label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
						{$i18n.t('Description')} ({$i18n.t('Optional')})
					</label>
					<textarea
						bind:value={newFolderDescription}
						class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
						rows="2"
						placeholder={$i18n.t('Enter folder description')}
					></textarea>
				</div>
				
				<div>
					<label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
						{$i18n.t('Color')}
					</label>
					<input
						type="color"
						bind:value={newFolderColor}
						class="w-full h-10 border border-gray-300 dark:border-gray-600 rounded-md"
					/>
				</div>
			</div>
			
			<div class="flex justify-end space-x-3 mt-6">
				<button
					type="button"
					class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600"
					on:click={() => showEditDialog = false}
				>
					{$i18n.t('Cancel')}
				</button>
				<button
					type="button"
					class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700"
					on:click={editFolder}
				>
					{$i18n.t('Save')}
				</button>
			</div>
		</div>
	</div>
{/if}

<!-- 刪除資料夾對話框 -->
{#if showDeleteDialog && currentFolder}
	<div class="fixed inset-0 bg-gray-900/50 flex items-center justify-center z-50">
		<div class="bg-white dark:bg-gray-800 rounded-lg p-6 w-96 max-w-full mx-4">
			<h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
				{$i18n.t('Delete Folder')}
			</h3>
			
			<p class="text-sm text-gray-600 dark:text-gray-400 mb-6">
				{$i18n.t('Are you sure you want to delete the folder')} "{currentFolder.name}"? 
				{$i18n.t('This action cannot be undone.')}
			</p>
			
			<div class="flex justify-end space-x-3">
				<button
					type="button"
					class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600"
					on:click={() => showDeleteDialog = false}
				>
					{$i18n.t('Cancel')}
				</button>
				<button
					type="button"
					class="px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-md hover:bg-red-700"
					on:click={deleteFolder}
				>
					{$i18n.t('Delete')}
				</button>
			</div>
		</div>
	</div>
{/if}

<style>
	.folder-tree {
		display: flex;
		flex-direction: column;
		height: 100%;
		background-color: white;
		border: 1px solid #e5e7eb;
		border-radius: 0.5rem;
	}

	:global(.dark) .folder-tree {
		background-color: #1f2937;
		border-color: #374151;
	}

	.folder-node {
		position: relative;
	}
</style>
