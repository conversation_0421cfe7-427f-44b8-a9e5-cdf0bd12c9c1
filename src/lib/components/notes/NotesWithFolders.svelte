<script lang="ts">
	import { onMount, getContext } from 'svelte';
	import { toast } from 'svelte-sonner';
	import { goto } from '$app/navigation';
	import { WEBUI_NAME, user, config, models, settings } from '$lib/stores';
	
	import { createNewNote, getNotes } from '$lib/apis/notes';
	import { getNotesInFolder, getNotesInRoot, getNoteFolderPath } from '$lib/apis/note-folders';
	import { getTimeRange } from '$lib/utils';
	
	import { PaneGroup, Pane, PaneResizer } from 'paneforge';
	
	import FolderTree from './FolderTree.svelte';
	import Search from '../icons/Search.svelte';
	import Plus from '../icons/Plus.svelte';
	import XMark from '../icons/XMark.svelte';
	import ChevronRight from '../icons/ChevronRight.svelte';
	import Spinner from '../common/Spinner.svelte';
	import ChatBubbleOval from '../icons/ChatBubbleOval.svelte';
	import Tooltip from '../common/Tooltip.svelte';
	import NotePanel from './NotePanel.svelte';
	import Chat from './NoteEditor/Chat.svelte';

	const i18n = getContext('i18n');

	let selectedFolderId: string | null = null;
	let expandedFolders: Set<string> = new Set();
	let notes: any = {};
	let filteredNotes: any = {};
	let query = '';
	let loaded = false;
	let folderPath: any[] = [];
	
	// AI Chat 相關變數
	let showPanel = false;
	let selectedPanel = 'chat';
	let selectedModelId = null;
	let messages = [];
	let editing = false;
	let streaming = false;
	let stopResponseFlag = false;

	// 初始化
	const init = async () => {
		await loadNotes();
	};

	// 載入筆記
	const loadNotes = async () => {
		try {
			let notesList = [];
			
			if (selectedFolderId === null) {
				// 載入根目錄筆記
				notesList = await getNotesInRoot(localStorage.token);
				// 清空資料夾路徑
				folderPath = [];
			} else {
				// 載入指定資料夾筆記
				notesList = await getNotesInFolder(localStorage.token, selectedFolderId);
				// 載入資料夾路徑
				folderPath = await getNoteFolderPath(localStorage.token, selectedFolderId);
			}

			// 按時間分組
			const groupedNotes = {};
			notesList.forEach((note) => {
				const timeRange = getTimeRange(note.updated_at / 1000000000);
				if (!groupedNotes[timeRange]) {
					groupedNotes[timeRange] = [];
				}
				groupedNotes[timeRange].push(note);
			});

			notes = groupedNotes;
			applyFilter();
		} catch (error) {
			toast.error(`${error}`);
		}
	};

	// 應用搜索過濾
	const applyFilter = () => {
		if (!query.trim()) {
			filteredNotes = notes;
			return;
		}

		const filtered = {};
		Object.keys(notes).forEach((timeRange) => {
			const filteredTimeRangeNotes = notes[timeRange].filter((note) =>
				note.title.toLowerCase().includes(query.toLowerCase()) ||
				(note.data?.content?.md || '').toLowerCase().includes(query.toLowerCase())
			);
			if (filteredTimeRangeNotes.length > 0) {
				filtered[timeRange] = filteredTimeRangeNotes;
			}
		});
		filteredNotes = filtered;
	};

	// 資料夾選擇處理
	const handleFolderSelected = async (event) => {
		selectedFolderId = event.detail.folderId;
		await loadNotes();
	};

	// 創建新筆記
	const createNoteHandler = async () => {
		try {
			const res = await createNewNote(localStorage.token, {
				title: new Date().toISOString().split('T')[0], // YYYY-MM-DD
				data: {
					content: {
						json: null,
						html: '',
						md: ''
					}
				},
				meta: null,
				access_control: {},
				folder_id: selectedFolderId
			});

			if (res) {
				goto(`/notes/${res.id}`);
			}
		} catch (error) {
			toast.error(`${error}`);
		}
	};

	// 監聽搜索查詢變化
	$: if (query !== undefined) {
		applyFilter();
	}

	// 初始化模型選擇
	const initModel = () => {
		if ($settings?.models) {
			selectedModelId = $settings?.models[0];
		} else if ($config?.default_models) {
			selectedModelId = $config?.default_models.split(',')[0];
		} else {
			selectedModelId = '';
		}

		if (selectedModelId) {
			const model = $models
				.filter((model) => model.id === selectedModelId && !(model?.info?.meta?.hidden ?? false))
				.find((model) => model.id === selectedModelId);

			if (!model) {
				selectedModelId = '';
			}
		}

		if (!selectedModelId) {
			selectedModelId = $models.at(0)?.id || '';
		}
	};

	// 創建虛擬筆記對象供 Chat 組件使用
	const createVirtualNote = () => {
		const notesList = Object.values(filteredNotes).flat();
		const notesContent = notesList.map(note => 
			`## ${note.title}\n${note.data?.content?.md || ''}`
		).join('\n\n');
		
		return {
			id: 'virtual-notes',
			title: selectedFolderId ? `資料夾筆記` : '所有筆記',
			data: {
				content: {
					md: notesContent,
					html: '',
					json: null
				},
				files: null
			}
		};
	};

	onMount(async () => {
		await init();
		initModel();
		loaded = true;
	});
</script>

<svelte:head>
	<title>
		{$i18n.t('Notes')} • {$WEBUI_NAME}
	</title>
</svelte:head>

<div id="note-container" class="w-full h-full">
<PaneGroup direction="horizontal" class="w-full h-full">
	<!-- 左側資料夾樹 -->
	<Pane defaultSize={30} minSize={15} maxSize={50} class="h-full">
		<div class="w-full h-full border-r border-gray-200 dark:border-gray-700">
			<FolderTree 
				bind:selectedFolderId 
				bind:expandedFolders 
				on:folderSelected={handleFolderSelected}
			/>
		</div>
	</Pane>

	<!-- 分隔線 -->
	<PaneResizer class="w-1 bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors cursor-col-resize" />

	<!-- 右側筆記列表 -->
	<Pane defaultSize={70} minSize={30} class="h-full flex flex-col w-full relative">
		<div class="w-full h-full overflow-hidden">
		{#if loaded}
			<!-- 頭部工具欄 -->
			<div class="flex flex-col gap-1 px-4 py-3 border-b border-gray-200 dark:border-gray-700">
				<!-- 面包屑導航 -->
				{#if folderPath.length > 0}
					<div class="flex items-center text-sm text-gray-600 dark:text-gray-400 mb-2">
						<button
							type="button"
							class="hover:text-gray-900 dark:hover:text-gray-200"
							on:click={() => handleFolderSelected({ detail: { folderId: null } })}
						>
							{$i18n.t('Root')}
						</button>
						{#each folderPath as folder, index}
							<ChevronRight className="w-4 h-4 mx-1" />
							<button
								type="button"
								class="hover:text-gray-900 dark:hover:text-gray-200 {index === folderPath.length - 1 ? 'font-medium text-gray-900 dark:text-gray-100' : ''}"
								on:click={() => handleFolderSelected({ detail: { folderId: folder.id } })}
							>
								{folder.name}
							</button>
						{/each}
					</div>
				{/if}

				<!-- 搜索和新建按鈕 -->
				<div class="flex items-center space-x-3">
					<div class="flex-1 flex items-center">
						<div class="mr-3">
							<Search className="w-4 h-4" />
						</div>
						<input
							class="w-full text-sm py-1 bg-transparent outline-none"
							bind:value={query}
							placeholder={$i18n.t('Search Notes')}
						/>
						{#if query}
							<button
								class="p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700"
								on:click={() => { query = ''; }}
							>
								<XMark className="w-3 h-3" />
							</button>
						{/if}
					</div>
					<button
						type="button"
						class="flex items-center px-3 py-1.5 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700"
						on:click={createNoteHandler}
					>
						<Plus className="w-4 h-4 mr-1" />
						{$i18n.t('New Note')}
					</button>
					<Tooltip placement="top" content={$i18n.t('Chat with Notes')} className="cursor-pointer">
						<button
							type="button"
							class="flex items-center px-3 py-1.5 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600"
							on:click={() => {
								if (showPanel && selectedPanel === 'chat') {
									showPanel = false;
								} else {
									if (!showPanel) {
										showPanel = true;
									}
									selectedPanel = 'chat';
								}
							}}
						>
							<ChatBubbleOval className="w-4 h-4 mr-1" />
							{$i18n.t('Chat')}
						</button>
					</Tooltip>
				</div>
			</div>

			<!-- 筆記列表 -->
			<div class="flex-1 overflow-y-auto p-4">
				{#if Object.keys(filteredNotes).length > 0}
					<div class="space-y-6">
						{#each Object.keys(filteredNotes) as timeRange}
							<div>
								<div class="text-xs text-gray-500 dark:text-gray-400 font-medium mb-3">
									{$i18n.t(timeRange)}
								</div>
								<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
									{#each filteredNotes[timeRange] as note (note.id)}
										<div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer">
											<a href={`/notes/${note.id}`} class="block">
												<div class="flex items-start justify-between mb-2">
													<h3 class="font-medium text-gray-900 dark:text-white line-clamp-1">
														{note.title}
													</h3>
													<span class="text-xs text-gray-500 dark:text-gray-400 ml-2">
														{new Date(note.updated_at / 1000000).toLocaleDateString()}
													</span>
												</div>
												{#if note.data?.content?.md}
													<p class="text-sm text-gray-600 dark:text-gray-400 line-clamp-3">
														{note.data.content.md.substring(0, 150)}
														{note.data.content.md.length > 150 ? '...' : ''}
													</p>
												{/if}
											</a>
										</div>
									{/each}
								</div>
							</div>
						{/each}
					</div>
				{:else}
					<div class="flex flex-col items-center justify-center h-64 text-center">
						<div class="text-gray-500 dark:text-gray-400 mb-4">
							{#if query}
								{$i18n.t('No notes found matching your search')}
							{:else if selectedFolderId}
								{$i18n.t('No notes in this folder')}
							{:else}
								{$i18n.t('No notes yet')}
							{/if}
						</div>
						<button
							type="button"
							class="flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700"
							on:click={createNoteHandler}
						>
							<Plus className="w-4 h-4 mr-2" />
							{$i18n.t('Create your first note')}
						</button>
					</div>
				{/if}
			</div>
		{:else}
			<div class="flex items-center justify-center h-full">
				<Spinner />
			</div>
		{/if}
		</div>
	</Pane>
	
	<!-- NotePanel for AI Chat -->
	<NotePanel bind:show={showPanel}>
		{#if selectedPanel === 'chat'}
			<Chat
				bind:show={showPanel}
				bind:selectedModelId
				bind:messages
				note={createVirtualNote()}
				bind:editing
				bind:streaming
				bind:stopResponseFlag
				editor={null}
				inputElement={null}
				selectedContent={null}
				files={[]}
				onInsert={() => {}}
				onStop={() => {
					stopResponseFlag = true;
				}}
				onEdited={() => {}}
				insertNoteHandler={() => {}}
				scrollToBottomHandler={() => {}}
			/>
		{/if}
	</NotePanel>
</PaneGroup>
</div>

<style>
	.line-clamp-1 {
		display: -webkit-box;
		-webkit-line-clamp: 1;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}
	
	.line-clamp-3 {
		display: -webkit-box;
		-webkit-line-clamp: 3;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}
</style>
