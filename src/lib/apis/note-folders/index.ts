import { WEBUI_API_BASE_URL } from '$lib/constants';

export type NoteFolderItem = {
	id: string;
	user_id: string;
	name: string;
	description?: string;
	color?: string;
	parent_id?: string;
	sort_order: number;
	access_control?: object;
	created_at: number;
	updated_at: number;
};

export type NoteFolderTreeNode = NoteFolderItem & {
	children: NoteFolderTreeNode[];
	note_count: number;
	total_note_count: number;
};

export type NoteFolderForm = {
	name: string;
	description?: string;
	color?: string;
	parent_id?: string;
	sort_order?: number;
	access_control?: object;
};

export type NoteFolderUpdateForm = {
	name?: string;
	description?: string;
	color?: string;
	parent_id?: string;
	sort_order?: number;
	access_control?: object;
};

export type NoteFolderMoveForm = {
	target_parent_id?: string;
	sort_order?: number;
};

// 獲取所有資料夾
export const getNoteFolders = async (token: string): Promise<NoteFolderItem[]> => {
	let error = null;

	const res = await fetch(`${WEBUI_API_BASE_URL}/notes/folders/`, {
		method: 'GET',
		headers: {
			Accept: 'application/json',
			'Content-Type': 'application/json',
			authorization: `Bearer ${token}`
		}
	})
		.then(async (res) => {
			if (!res.ok) throw await res.json();
			return res.json();
		})
		.catch((err) => {
			error = err.detail;
			console.error(err);
			return null;
		});

	if (error) {
		throw error;
	}

	return res;
};

// 獲取資料夾樹狀結構
export const getNoteFolderTree = async (token: string): Promise<NoteFolderTreeNode[]> => {
	let error = null;

	const res = await fetch(`${WEBUI_API_BASE_URL}/notes/folders/tree`, {
		method: 'GET',
		headers: {
			Accept: 'application/json',
			'Content-Type': 'application/json',
			authorization: `Bearer ${token}`
		}
	})
		.then(async (res) => {
			if (!res.ok) throw await res.json();
			return res.json();
		})
		.catch((err) => {
			error = err.detail;
			console.error(err);
			return null;
		});

	if (error) {
		throw error;
	}

	return res;
};

// 創建新資料夾
export const createNoteFolder = async (
	token: string,
	folder: NoteFolderForm
): Promise<NoteFolderItem> => {
	let error = null;

	const res = await fetch(`${WEBUI_API_BASE_URL}/notes/folders/`, {
		method: 'POST',
		headers: {
			Accept: 'application/json',
			'Content-Type': 'application/json',
			authorization: `Bearer ${token}`
		},
		body: JSON.stringify(folder)
	})
		.then(async (res) => {
			if (!res.ok) throw await res.json();
			return res.json();
		})
		.catch((err) => {
			error = err.detail;
			console.error(err);
			return null;
		});

	if (error) {
		throw error;
	}

	return res;
};

// 根據ID獲取資料夾
export const getNoteFolderById = async (
	token: string,
	folderId: string
): Promise<NoteFolderItem> => {
	let error = null;

	const res = await fetch(`${WEBUI_API_BASE_URL}/notes/folders/${folderId}`, {
		method: 'GET',
		headers: {
			Accept: 'application/json',
			'Content-Type': 'application/json',
			authorization: `Bearer ${token}`
		}
	})
		.then(async (res) => {
			if (!res.ok) throw await res.json();
			return res.json();
		})
		.catch((err) => {
			error = err.detail;
			console.error(err);
			return null;
		});

	if (error) {
		throw error;
	}

	return res;
};

// 更新資料夾
export const updateNoteFolder = async (
	token: string,
	folderId: string,
	folder: NoteFolderUpdateForm
): Promise<NoteFolderItem> => {
	let error = null;

	const res = await fetch(`${WEBUI_API_BASE_URL}/notes/folders/${folderId}`, {
		method: 'PUT',
		headers: {
			Accept: 'application/json',
			'Content-Type': 'application/json',
			authorization: `Bearer ${token}`
		},
		body: JSON.stringify(folder)
	})
		.then(async (res) => {
			if (!res.ok) throw await res.json();
			return res.json();
		})
		.catch((err) => {
			error = err.detail;
			console.error(err);
			return null;
		});

	if (error) {
		throw error;
	}

	return res;
};

// 刪除資料夾
export const deleteNoteFolder = async (token: string, folderId: string): Promise<boolean> => {
	let error = null;

	const res = await fetch(`${WEBUI_API_BASE_URL}/notes/folders/${folderId}`, {
		method: 'DELETE',
		headers: {
			Accept: 'application/json',
			'Content-Type': 'application/json',
			authorization: `Bearer ${token}`
		}
	})
		.then(async (res) => {
			if (!res.ok) throw await res.json();
			return res.json();
		})
		.catch((err) => {
			error = err.detail;
			console.error(err);
			return null;
		});

	if (error) {
		throw error;
	}

	return true;
};

// 移動資料夾
export const moveNoteFolder = async (
	token: string,
	folderId: string,
	moveData: NoteFolderMoveForm
): Promise<NoteFolderItem> => {
	let error = null;

	const res = await fetch(`${WEBUI_API_BASE_URL}/notes/folders/${folderId}/move`, {
		method: 'POST',
		headers: {
			Accept: 'application/json',
			'Content-Type': 'application/json',
			authorization: `Bearer ${token}`
		},
		body: JSON.stringify(moveData)
	})
		.then(async (res) => {
			if (!res.ok) throw await res.json();
			return res.json();
		})
		.catch((err) => {
			error = err.detail;
			console.error(err);
			return null;
		});

	if (error) {
		throw error;
	}

	return res;
};

// 獲取資料夾路徑
export const getNoteFolderPath = async (
	token: string,
	folderId: string
): Promise<NoteFolderItem[]> => {
	let error = null;

	const res = await fetch(`${WEBUI_API_BASE_URL}/notes/folders/${folderId}/path`, {
		method: 'GET',
		headers: {
			Accept: 'application/json',
			'Content-Type': 'application/json',
			authorization: `Bearer ${token}`
		}
	})
		.then(async (res) => {
			if (!res.ok) throw await res.json();
			return res.json();
		})
		.catch((err) => {
			error = err.detail;
			console.error(err);
			return null;
		});

	if (error) {
		throw error;
	}

	return res;
};

// 移動筆記到資料夾
export const moveNoteToFolder = async (
	token: string,
	noteId: string,
	folderId?: string
): Promise<any> => {
	let error = null;

	const res = await fetch(`${WEBUI_API_BASE_URL}/notes/${noteId}/move`, {
		method: 'POST',
		headers: {
			Accept: 'application/json',
			'Content-Type': 'application/json',
			authorization: `Bearer ${token}`
		},
		body: JSON.stringify({ folder_id: folderId })
	})
		.then(async (res) => {
			if (!res.ok) throw await res.json();
			return res.json();
		})
		.catch((err) => {
			error = err.detail;
			console.error(err);
			return null;
		});

	if (error) {
		throw error;
	}

	return res;
};

// 獲取資料夾中的筆記
export const getNotesInFolder = async (token: string, folderId: string): Promise<any[]> => {
	let error = null;

	const res = await fetch(`${WEBUI_API_BASE_URL}/notes/folders/${folderId}/notes`, {
		method: 'GET',
		headers: {
			Accept: 'application/json',
			'Content-Type': 'application/json',
			authorization: `Bearer ${token}`
		}
	})
		.then(async (res) => {
			if (!res.ok) throw await res.json();
			return res.json();
		})
		.catch((err) => {
			error = err.detail;
			console.error(err);
			return null;
		});

	if (error) {
		throw error;
	}

	return res;
};

// 獲取根目錄中的筆記
export const getNotesInRoot = async (token: string): Promise<any[]> => {
	let error = null;

	const res = await fetch(`${WEBUI_API_BASE_URL}/notes/folders/root/notes`, {
		method: 'GET',
		headers: {
			Accept: 'application/json',
			'Content-Type': 'application/json',
			authorization: `Bearer ${token}`
		}
	})
		.then(async (res) => {
			if (!res.ok) throw await res.json();
			return res.json();
		})
		.catch((err) => {
			error = err.detail;
			console.error(err);
			return null;
		});

	if (error) {
		throw error;
	}

	return res;
};
