<script>
	import { showArchivedChats, showSidebar, user } from '$lib/stores';
	import { getContext } from 'svelte';

	const i18n = getContext('i18n');

	import MenuLines from '$lib/components/icons/MenuLines.svelte';
	import UserMenu from '$lib/components/layout/Sidebar/UserMenu.svelte';
	import Notes from '$lib/components/notes/NotesWithFolders.svelte';
</script>

<div
	class=" flex flex-col w-full h-full transition-width duration-200 ease-in-out {$showSidebar
		? 'md:max-w-[calc(100%-260px)]'
		: ''} max-w-full"
>
	<div class=" pb-1 flex-1 max-h-full overflow-y-auto @container">
		<Notes />
	</div>
</div>
